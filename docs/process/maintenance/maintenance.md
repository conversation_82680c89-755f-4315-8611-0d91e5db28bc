---
title: 服务器维护
slug: /maintenance
sidebar_position: 2
---

# 服务器维护

当你搭建好服务器后，需要做一些维护工作来保证服务器良好的运行，除非你压根不考虑玩家数据安全问题。

## 安装防熊插件

类似 CoreProtect 等防熊插件可以记录玩家的几乎所有操作。

如果遇到熊孩子恶意毁坏，可以使用插件回溯功能单独回溯熊孩子的操作。

即使在区块出现损坏的情况下，这些插件也能在一定程度上减少区块损坏所造成的影响。见 [防熊插件](https://nitwikit.8aka.org/Java/AntiGrief)。

## 网络防护

服务器面临各种网络攻击威胁，包括 DDoS、假人攻击、爆破攻击等。了解攻击类型和防御方法对服务器安全至关重要。

详见 [网络防护](network-protect/network-protect.md)。

## 检查日志

[日志](/docs/start/basic/basic.md)中包含了大量的玩家、插件、服务器行为。

每次维护服务器时请务必查看服务器近期的日志。

对于 ERROR 等级的报错请立即处理，自行在网络查询或咨询其他人。

对于 WARN 等级的报错，请尝试通过翻译等手段了解 warn 的内容，自行在网络查询或咨询其他人。

:::warning

注意！咨询其他人时请先查看 [如何向大佬求助](/docs/start/ask-for-help.md)

:::
