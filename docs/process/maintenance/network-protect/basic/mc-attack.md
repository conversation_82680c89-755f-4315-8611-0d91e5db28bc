---
title: MC 攻击
sidebar_position: 1
---

# MC 攻击

在服务器上运行并绑定了指定地址和端口的应用程序，可以在这一层接受连接。

应用层的攻击往往意味着针对某个应用程序发起的攻击。

通常攻击者会利用应用中的漏洞，来让应用占用更多的计算机资源，或者通过大量请求使带宽不堪重负，使服务器难以处理新连接。

## 假人攻击

:::info
本处仅讨论关于 Minecraft 服务器网络安全的概念，不涉及关于“生电”中假人的相关概念。
:::

假人攻击（Fake Player Attack），是指攻击者利用技术手段，模拟正常玩家的行为并向 Minecraft 服务器发起非正常请求，进而使得 Minecraft 服务器因各种原因而瘫痪崩溃或被渗透的一种攻击方式。

假人对服务器造成的影响不尽相同，包括但不限于：
1. 大量假人涌入服务器使服务器达到最大人数限制从而导致正常玩家无法进入服务器。
2. 假人的加入与退出操作可能会导致某些开发不完全的插件在处理此类逻辑时发生内存泄漏和其他非预期的异常。
3. 利用聊天框发送垃圾信息刷屏，扰乱正常消息处理与聊天体验。

在某些程度上，假人攻击类似于 Web 安全中的 [CC 攻击](https://www.qiuwenbaike.cn/wiki/拒绝服务攻击#资源消耗型攻击)。

## MOTD (状态请求) 攻击

简单来说，就是向服务器请求状态 (也就是 Ping)，玩家每次 Ping 服务器时，服务器将返回一个 MOTD。

由于 MOTD 中包含图片和文字信息，大量的请求会占满服务器带宽，使服务器难以处理新的连接。

Minecraft 后端服务器一般是不会对 Ping 进行过滤和记录的，这会导致 MOTD 攻击难以察觉。

但是对于 Velocity / BungeeCord 等反向代理服务端，默认 Ping 服务器的行为是会被记录的，类似于：

```text
[/127.0.0.1:61647] <-> InitialHandler has pinged
```

:::info

可以通过调整设置 `log_pings` (BungeeCord) 或 `show-ping-requests` (Velocity) 来启用或禁用反向代理在控制台输出 Ping 日志。

:::

## 反射攻击

反射攻击是指攻击者利用 Minecraft 服务器的一些漏洞，向其他服务器发送大量请求，从而导致其他服务器不堪重负的一种攻击方式。

最著名的就是 [Geyser RakNet 放大攻击](https://geysermc.org/blog/raknet-amplification-attack/),
可以将攻击者的流量放大理论上最大倍数 22,000 倍



